// D3.js Marketplace Analytics Dashboard Island
// Advanced interactive visualizations for marketplace analytics with real-time capabilities

import { useEffect, useRef, useState } from "preact/hooks";
import { useSignal } from "@preact/signals";
import * as d3 from "d3";
import { User } from "../../utils/auth.ts";



interface CohortSegment {
  id: string;
  name?: string;
  startDate: string;
  size: number;
  retentionRates: number[];
  cohortType?: 'partnership_acquisition' | 'cross_tenant_collaboration' | 'referral_program' | 'joint_marketing_campaign' | 'organic';
  partnershipType?: 'strategic' | 'affiliate' | 'integration';
  acquisitionChannel?: 'referral' | 'collaboration' | 'cross_promotion' | 'direct';
  partnershipId?: string;
  partnerName?: string;
  revenueData?: number[];
  conversionRates?: number[];
}

interface MarketplaceCohortComparison {
  vs_organic_retention: number;
  vs_average_partnership: number;
  revenue_multiplier: number;
  performance_tier: 'top_quartile' | 'above_average' | 'average' | 'below_average';
}

interface CohortAnalysisData {
  segments: CohortSegment[];
  overview: {
    totalCohorts: number;
    avgRetentionRate: number;
    bestPerformingCohort: string;
    organicRetentionBaseline: number;
    partnershipRetentionAverage: number;
  };
  partnership_cohorts?: {
    partnership_id: string;
    partner_name: string;
    acquisition_channel: 'referral' | 'collaboration' | 'cross_promotion';
    segments: CohortSegment[];
    comparative_metrics: MarketplaceCohortComparison;
  }[];
  cross_tenant_analysis?: {
    tenant_pair: string;
    shared_customer_retention: number[];
    collaboration_effectiveness: number;
  }[];
}

interface CohortFilters {
  cohortType: 'all' | 'partnership_acquisition' | 'cross_tenant_collaboration' | 'referral_program' | 'joint_marketing_campaign' | 'organic';
  partnershipType: 'all' | 'strategic' | 'affiliate' | 'integration';
  acquisitionChannel: 'all' | 'referral' | 'collaboration' | 'cross_promotion' | 'direct';
  timeRange: '30d' | '90d' | '180d' | '1y';
  comparisonMode: 'vs_organic' | 'vs_average' | 'vs_top_quartile' | 'absolute';
}

interface MarketplaceAnalyticsData {
  partnership_metrics: Array<{
    hour: string;
    partnership_id: string;
    event_count: number;
    total_revenue: number;
    conversion_rate: number;
    unique_customers: number;
  }>;
  network_trends: Array<{
    day: string;
    daily_events: number;
    daily_revenue: number;
    unique_customers: number;
    daily_conversion_rate: number;
  }>;
  tenant_activity: Array<{
    hour: string;
    tenant_id: string;
    direction: string;
    event_count: number;
    total_revenue: number;
    unique_partners: number;
  }>;
  realtime_performance: Array<{
    quarter_hour: string;
    partnership_id: string;
    events_15min: number;
    revenue_15min: number;
    conversion_rate_15min: number;
  }>;
  summary_stats: {
    total_partnerships: number;
    active_partnerships: number;
    total_revenue_7d: number;
    total_events_7d: number;
    avg_conversion_rate: number;
    top_partnerships: Array<{
      partnership_id: string;
      total_revenue: number;
      total_events: number;
    }>;
  };
  advanced_analytics?: {
    cohort_analysis?: CohortAnalysisData;
    clv_analysis?: unknown;
    funnel_analysis?: unknown;
    predictive_analysis?: unknown;
  };
}

interface D3MarketplaceAnalyticsDashboardProps {
  analyticsData: MarketplaceAnalyticsData;
  user: User;
  onTimeFrameChange?: (timeFrame: string) => void;
  onMetricChange?: (metric: string) => void;
  onDataExport?: (data: MarketplaceAnalyticsData, format: string) => void;
}





export default function D3MarketplaceAnalyticsDashboard({
  analyticsData,
  user: _user,
  onTimeFrameChange,
  onMetricChange,
  onDataExport
}: D3MarketplaceAnalyticsDashboardProps) {
  console.log('D3MarketplaceAnalyticsDashboard component rendered with data:', {
    hasAnalyticsData: !!analyticsData,
    dataKeys: analyticsData ? Object.keys(analyticsData) : [],
    networkTrendsLength: analyticsData?.network_trends?.length || 0,
    topPartnershipsLength: analyticsData?.summary_stats?.top_partnerships?.length || 0,
    realtimePerformanceLength: analyticsData?.realtime_performance?.length || 0
  });

  // Refs for D3 chart containers
  const networkTrendsRef = useRef<HTMLDivElement>(null);
  const partnershipMetricsRef = useRef<HTMLDivElement>(null);
  const realtimePerformanceRef = useRef<HTMLDivElement>(null);
  const cohortAnalysisRef = useRef<HTMLDivElement>(null);

  // State management
  const [selectedTimeFrame, setSelectedTimeFrame] = useState('7d');
  const [selectedMetric, setSelectedMetric] = useState('revenue');
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(true);
  const [_loading, _setLoading] = useState(false);
  const [_realtimeData, setRealtimeData] = useState<unknown[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('disconnected');
  const [lastUpdate, setLastUpdate] = useState<string>('');
  const [chartsInitialized, setChartsInitialized] = useState<{
    networkTrends: boolean;
    partnershipMetrics: boolean;
    realtimePerformance: boolean;
    cohortAnalysis: boolean;
  }>({
    networkTrends: false,
    partnershipMetrics: false,
    realtimePerformance: false,
    cohortAnalysis: false
  });

  // Enhanced cohort filtering state
  const [cohortFilters, setCohortFilters] = useState<CohortFilters>({
    cohortType: 'all',
    partnershipType: 'all',
    acquisitionChannel: 'all',
    timeRange: '180d',
    comparisonMode: 'vs_organic'
  });
  const [showComparison, setShowComparison] = useState(false);

  // Signals for reactive updates
  const chartDimensions = useSignal({ width: 800, height: 400 });
  const isDarkMode = useSignal(false);

  // Real-time streaming connection
  useEffect(() => {
    if (!isRealTimeEnabled) return;

    let eventSource: EventSource | null = null;

    const connectToStream = () => {
      setConnectionStatus('connecting');
      eventSource = new EventSource('/api/marketplace/analytics/stream');

      eventSource.onopen = () => {
        console.log('Connected to real-time analytics stream');
        setConnectionStatus('connected');
      };

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          if (data.type === 'heartbeat') {
            setLastUpdate(new Date().toLocaleTimeString());
            return;
          }

          if (data.event_type) {
            setRealtimeData(prev => [...prev.slice(-19), data]); // Keep last 20 updates
            setLastUpdate(new Date().toLocaleTimeString());

            // Update charts with new data
            if (data.event_type === 'performance_update' || data.event_type === 'revenue_update') {
              updateRealtimeCharts(data);
            }
          }
        } catch (error) {
          console.error('Error parsing real-time data:', error);
        }
      };

      eventSource.onerror = (error) => {
        console.error('Real-time stream error:', error);
        setConnectionStatus('disconnected');

        // Attempt to reconnect after 5 seconds
        setTimeout(() => {
          if (isRealTimeEnabled) {
            connectToStream();
          }
        }, 5000);
      };
    };

    connectToStream();

    return () => {
      if (eventSource) {
        eventSource.close();
        setConnectionStatus('disconnected');
      }
    };
  }, [isRealTimeEnabled]);

  // Update charts with real-time data
  const updateRealtimeCharts = (data: { data?: { network_performance?: { total_events_1min: number; total_revenue_1min: number; avg_conversion_rate: number } } }) => {
    if (data.data?.network_performance) {
      // Update real-time performance chart
      const newDataPoint = {
        time: new Date(),
        events_15min: data.data.network_performance.total_events_1min * 15, // Simulate 15-min aggregation
        revenue_15min: data.data.network_performance.total_revenue_1min * 15,
        conversion_rate_15min: data.data.network_performance.avg_conversion_rate
      };

      // Add to existing realtime_performance data
      analyticsData.realtime_performance = [
        ...analyticsData.realtime_performance.slice(-19), // Keep last 19 points
        {
          quarter_hour: newDataPoint.time.toISOString(),
          partnership_id: 'realtime-aggregate',
          events_15min: newDataPoint.events_15min,
          revenue_15min: newDataPoint.revenue_15min,
          conversion_rate_15min: newDataPoint.conversion_rate_15min
        }
      ];

      // Re-render the real-time chart
      initializeRealtimePerformanceChart();
    }
  };

  // Chart initialization function declarations
  const initializeNetworkTrendsChart = () => {
    console.log('Initializing Network Trends Chart...');
    if (!networkTrendsRef.current) {
      console.log('Network trends ref not available');
      return;
    }

    // Use real data if available, otherwise generate mock data
    const trendsData = (analyticsData.network_trends && analyticsData.network_trends.length > 0)
      ? analyticsData.network_trends
      : generateMockNetworkTrends();

    console.log('Network trends data:', {
      realDataLength: analyticsData.network_trends?.length || 0,
      trendsDataLength: trendsData.length,
      sampleData: trendsData[0]
    });

    if (!trendsData.length) {
      console.log('No network trends data available');
      return;
    }

    try {
      if (!networkTrendsRef.current) {
        throw new Error('Network trends container not available');
      }

      const container = d3.select(networkTrendsRef.current);
      container.selectAll("*").remove();

      console.log('Network trends container selected, starting chart creation...');

      // Set up chart dimensions
      const margin = { top: 20, right: 30, bottom: 40, left: 60 };
      const width = chartDimensions.value.width - margin.left - margin.right;
      const height = 300 - margin.top - margin.bottom;

      // Create SVG
      const svg = container.append("svg")
        .attr("width", width + margin.left + margin.right)
        .attr("height", height + margin.top + margin.bottom);

      const g = svg.append("g")
        .attr("transform", `translate(${margin.left},${margin.top})`);

      // Parse dates and prepare data
      const parseDate = d3.timeParse("%Y-%m-%d");
      const data = trendsData.map(d => ({
        date: parseDate(d.day),
        events: d.daily_events,
        revenue: d.daily_revenue,
        customers: d.unique_customers,
        conversionRate: d.daily_conversion_rate
      })).filter(d => d.date !== null).map(d => ({
        date: d.date as Date,
        events: d.events,
        revenue: d.revenue,
        customers: d.customers,
        conversionRate: d.conversionRate
      }));

      // Set up scales
      const xExtent = d3.extent(data, d => d.date) as [Date, Date];
      const xScale = d3.scaleTime()
        .domain(xExtent)
        .range([0, width]);

      const maxValue = d3.max(data, d => selectedMetric === 'revenue' ? d.revenue : d.events) || 0;
      const yScale = d3.scaleLinear()
        .domain([0, maxValue])
        .nice()
        .range([height, 0]);

      // Create line generator
      const line = d3.line<{date: Date, events: number, revenue: number, customers: number, conversionRate: number}>()
        .x(d => xScale(d.date))
        .y(d => yScale(selectedMetric === 'revenue' ? d.revenue : d.events))
        .curve(d3.curveMonotoneX);

      // Add axes
      g.append("g")
        .attr("transform", `translate(0,${height})`)
        .call(d3.axisBottom(xScale).tickFormat((d) => d3.timeFormat("%m/%d")(d as Date)))
        .selectAll("text")
        .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151");

      g.append("g")
        .call(d3.axisLeft(yScale))
        .selectAll("text")
        .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151");

      // Add axis lines
      g.selectAll(".domain, .tick line")
        .style("stroke", isDarkMode.value ? "#4B5563" : "#E5E7EB");

      // Add the line
      g.append("path")
        .datum(data)
        .attr("fill", "none")
        .attr("stroke", "#3B82F6")
        .attr("stroke-width", 2)
        .attr("d", line);

      // Add dots
      g.selectAll(".dot")
        .data(data)
        .enter().append("circle")
        .attr("class", "dot")
        .attr("cx", d => xScale(d.date))
        .attr("cy", d => yScale(selectedMetric === 'revenue' ? d.revenue : d.events))
        .attr("r", 4)
        .attr("fill", "#3B82F6")
        .on("mouseover", function(event, d) {
          // Create tooltip
          const tooltip = container.append("div")
            .attr("class", "tooltip")
            .style("position", "absolute")
            .style("background", isDarkMode.value ? "#1F2937" : "#FFFFFF")
            .style("border", `1px solid ${isDarkMode.value ? "#374151" : "#E5E7EB"}`)
            .style("border-radius", "6px")
            .style("padding", "8px")
            .style("font-size", "12px")
            .style("color", isDarkMode.value ? "#F3F4F6" : "#1F2937")
            .style("box-shadow", "0 4px 6px -1px rgba(0, 0, 0, 0.1)")
            .style("pointer-events", "none")
            .style("z-index", "1000");

          tooltip.html(`
            <div><strong>${d3.timeFormat("%B %d")(d.date)}</strong></div>
            <div>Events: ${d.events.toLocaleString()}</div>
            <div>Revenue: $${d.revenue.toLocaleString()}</div>
            <div>Customers: ${d.customers.toLocaleString()}</div>
            <div>Conversion: ${d.conversionRate.toFixed(2)}%</div>
          `);

          // Position tooltip
          const rect = networkTrendsRef.current!.getBoundingClientRect();
          tooltip
            .style("left", (event.clientX - rect.left + 10) + "px")
            .style("top", (event.clientY - rect.top - 10) + "px");
        })
        .on("mouseout", function() {
          container.selectAll(".tooltip").remove();
        });

      // Add chart title
      g.append("text")
        .attr("x", width / 2)
        .attr("y", -5)
        .attr("text-anchor", "middle")
        .style("font-size", "14px")
        .style("font-weight", "600")
        .style("fill", isDarkMode.value ? "#F3F4F6" : "#1F2937")
        .text(`Network Trends - ${selectedMetric === 'revenue' ? 'Revenue' : 'Events'}`);

      console.log('Network trends chart created successfully with', trendsData.length, 'data points');

      // Update chart status after successful creation
      setChartsInitialized(prev => ({ ...prev, networkTrends: true }));

      // Update debug status
      const debugElement = document.getElementById('chart-debug-status');
      if (debugElement) {
        debugElement.textContent = 'Network Trends chart initialized successfully';
        debugElement.style.color = '#10B981';
      }
    } catch (error) {
      console.error('Error creating network trends chart:', error);

      // Update debug status with error
      const debugElement = document.getElementById('chart-debug-status');
      if (debugElement) {
        debugElement.textContent = `Network Trends chart error: ${error instanceof Error ? error.message : String(error)}`;
        debugElement.style.color = '#EF4444';
      }

      // Set chart status to failed
      setChartsInitialized(prev => ({ ...prev, networkTrends: false }));
    }
  };

  const initializePartnershipMetricsChart = () => {
    console.log('Initializing Partnership Metrics Chart...');
    if (!partnershipMetricsRef.current) {
      console.log('Partnership metrics ref not available');
      return;
    }

    // Use real data if available, otherwise generate mock data
    const partnershipsData = (analyticsData.summary_stats?.top_partnerships && analyticsData.summary_stats.top_partnerships.length > 0)
      ? analyticsData.summary_stats.top_partnerships
      : generateMockTopPartnerships();

    console.log('Partnership metrics data:', {
      realDataLength: analyticsData.summary_stats?.top_partnerships?.length || 0,
      partnershipsDataLength: partnershipsData.length,
      sampleData: partnershipsData[0]
    });

    if (!partnershipsData.length) {
      console.log('No partnership metrics data available');
      return;
    }

    try {
      if (!partnershipMetricsRef.current) {
        throw new Error('Partnership metrics container not available');
      }

      const container = d3.select(partnershipMetricsRef.current);
      container.selectAll("*").remove();

      console.log('Partnership metrics container selected, starting chart creation...');

      // Set up chart dimensions
      const margin = { top: 20, right: 30, bottom: 60, left: 80 };
      const width = chartDimensions.value.width - margin.left - margin.right;
      const height = 300 - margin.top - margin.bottom;

      // Create SVG
      const svg = container.append("svg")
        .attr("width", width + margin.left + margin.right)
        .attr("height", height + margin.top + margin.bottom);

      const g = svg.append("g")
        .attr("transform", `translate(${margin.left},${margin.top})`);

      // Prepare data
      const data = partnershipsData.map(d => ({
        id: d.partnership_id,
        revenue: d.total_revenue,
        events: d.total_events
      }));

      // Set up scales
      const xScale = d3.scaleBand()
        .domain(data.map(d => d.id))
        .range([0, width])
        .padding(0.1);

      const yScale = d3.scaleLinear()
        .domain([0, d3.max(data, d => d.revenue) || 0])
        .nice()
        .range([height, 0]);

      // Add axes
      g.append("g")
        .attr("transform", `translate(0,${height})`)
        .call(d3.axisBottom(xScale))
        .selectAll("text")
        .style("text-anchor", "end")
        .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151")
        .attr("dx", "-.8em")
        .attr("dy", ".15em")
        .attr("transform", "rotate(-45)");

      g.append("g")
        .call(d3.axisLeft(yScale).tickFormat(d => `$${(d as number / 1000).toFixed(0)}K`))
        .selectAll("text")
        .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151");

      // Add axis lines
      g.selectAll(".domain, .tick line")
        .style("stroke", isDarkMode.value ? "#4B5563" : "#E5E7EB");

      // Add bars
      g.selectAll(".bar")
        .data(data)
        .enter().append("rect")
        .attr("class", "bar")
        .attr("x", d => xScale(d.id) || 0)
        .attr("width", xScale.bandwidth())
        .attr("y", d => yScale(d.revenue))
        .attr("height", d => height - yScale(d.revenue))
        .attr("fill", "#10B981")
        .on("mouseover", function(event, d) {
          // Highlight bar
          d3.select(this).attr("fill", "#059669");

          // Create tooltip
          const tooltip = container.append("div")
            .attr("class", "tooltip")
            .style("position", "absolute")
            .style("background", isDarkMode.value ? "#1F2937" : "#FFFFFF")
            .style("border", `1px solid ${isDarkMode.value ? "#374151" : "#E5E7EB"}`)
            .style("border-radius", "6px")
            .style("padding", "8px")
            .style("font-size", "12px")
            .style("color", isDarkMode.value ? "#F3F4F6" : "#1F2937")
            .style("box-shadow", "0 4px 6px -1px rgba(0, 0, 0, 0.1)")
            .style("pointer-events", "none")
            .style("z-index", "1000");

          tooltip.html(`
            <div><strong>${d.id}</strong></div>
            <div>Revenue: $${d.revenue.toLocaleString()}</div>
            <div>Events: ${d.events.toLocaleString()}</div>
          `);

          // Position tooltip
          const rect = partnershipMetricsRef.current!.getBoundingClientRect();
          tooltip
            .style("left", (event.clientX - rect.left + 10) + "px")
            .style("top", (event.clientY - rect.top - 10) + "px");
        })
        .on("mouseout", function() {
          // Reset bar color
          d3.select(this).attr("fill", "#10B981");
          // Remove tooltip
          container.selectAll(".tooltip").remove();
        });

      // Add value labels on bars
      g.selectAll(".label")
        .data(data)
        .enter().append("text")
        .attr("class", "label")
        .attr("x", d => (xScale(d.id) || 0) + xScale.bandwidth() / 2)
        .attr("y", d => yScale(d.revenue) - 5)
        .attr("text-anchor", "middle")
        .style("font-size", "11px")
        .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151")
        .text(d => `$${(d.revenue / 1000).toFixed(0)}K`);

      // Add chart title
      g.append("text")
        .attr("x", width / 2)
        .attr("y", -5)
        .attr("text-anchor", "middle")
        .style("font-size", "14px")
        .style("font-weight", "600")
        .style("fill", isDarkMode.value ? "#F3F4F6" : "#1F2937")
        .text("Top Partnerships by Revenue");

      console.log('Partnership metrics chart created successfully with', partnershipsData.length, 'partnerships');

      // Update chart status after successful creation
      setChartsInitialized(prev => ({ ...prev, partnershipMetrics: true }));

      // Update debug status
      const debugElement = document.getElementById('chart-debug-status');
      if (debugElement) {
        debugElement.textContent = 'Partnership Metrics chart initialized successfully';
        debugElement.style.color = '#10B981';
      }
    } catch (error) {
      console.error('Error creating partnership metrics chart:', error);

      // Update debug status with error
      const debugElement = document.getElementById('chart-debug-status');
      if (debugElement) {
        debugElement.textContent = `Partnership Metrics chart error: ${error instanceof Error ? error.message : String(error)}`;
        debugElement.style.color = '#EF4444';
      }

      // Set chart status to failed
      setChartsInitialized(prev => ({ ...prev, partnershipMetrics: false }));
    }
  };

  const initializeRealtimePerformanceChart = () => {
    console.log('Initializing Realtime Performance Chart...');
    if (!realtimePerformanceRef.current) {
      console.log('Realtime performance ref not available');
      return;
    }

    // Use real data if available, otherwise generate mock data
    const performanceData = (analyticsData.realtime_performance && analyticsData.realtime_performance.length > 0)
      ? analyticsData.realtime_performance
      : generateMockRealtimePerformance();

    console.log('Realtime performance data:', {
      realDataLength: analyticsData.realtime_performance?.length || 0,
      performanceDataLength: performanceData.length,
      sampleData: performanceData[0]
    });

    if (!performanceData.length) {
      console.log('No realtime performance data available');
      return;
    }

    try {
      if (!realtimePerformanceRef.current) {
        throw new Error('Realtime performance container not available');
      }

      const container = d3.select(realtimePerformanceRef.current);
      container.selectAll("*").remove();

      console.log('Realtime performance container selected, starting chart creation...');

      // Set up chart dimensions
      const margin = { top: 20, right: 30, bottom: 40, left: 60 };
      const width = chartDimensions.value.width - margin.left - margin.right;
      const height = 300 - margin.top - margin.bottom;

      // Create SVG
      const svg = container.append("svg")
        .attr("width", width + margin.left + margin.right)
        .attr("height", height + margin.top + margin.bottom);

      const g = svg.append("g")
        .attr("transform", `translate(${margin.left},${margin.top})`);

      // Parse timestamps and prepare data
      const parseTime = d3.timeParse("%H:%M");
      const data = performanceData.map((d, i) => ({
        time: parseTime(d.quarter_hour) || new Date(Date.now() - (performanceData.length - i) * 15 * 60 * 1000),
        value: d.events_15min,
        revenue: d.revenue_15min,
        conversionRate: d.conversion_rate_15min,
        partnershipId: d.partnership_id
      })).sort((a, b) => a.time.getTime() - b.time.getTime());

      // Set up scales
      const xScale = d3.scaleTime()
        .domain(d3.extent(data, d => d.time) as [Date, Date])
        .range([0, width]);

      const yScale = d3.scaleLinear()
        .domain([0, d3.max(data, d => d.value) || 100])
        .nice()
        .range([height, 0]);

      // Create area generator
      const area = d3.area<{time: Date, value: number, revenue: number, conversionRate: number, partnershipId: string}>()
        .x(d => xScale(d.time))
        .y0(height)
        .y1(d => yScale(d.value))
        .curve(d3.curveMonotoneX);

      // Create line generator
      const line = d3.line<{time: Date, value: number, revenue: number, conversionRate: number, partnershipId: string}>()
        .x(d => xScale(d.time))
        .y(d => yScale(d.value))
        .curve(d3.curveMonotoneX);

      // Add gradient definition
      const gradient = svg.append("defs")
        .append("linearGradient")
        .attr("id", "area-gradient")
        .attr("gradientUnits", "userSpaceOnUse")
        .attr("x1", 0).attr("y1", height)
        .attr("x2", 0).attr("y2", 0);

      gradient.append("stop")
        .attr("offset", "0%")
        .attr("stop-color", "#8B5CF6")
        .attr("stop-opacity", 0.1);

      gradient.append("stop")
        .attr("offset", "100%")
        .attr("stop-color", "#8B5CF6")
        .attr("stop-opacity", 0.6);

      // Add axes
      g.append("g")
        .attr("transform", `translate(0,${height})`)
        .call(d3.axisBottom(xScale).tickFormat((d) => d3.timeFormat("%H:%M")(d as Date)))
        .selectAll("text")
        .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151");

      g.append("g")
        .call(d3.axisLeft(yScale))
        .selectAll("text")
        .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151");

      // Add axis lines
      g.selectAll(".domain, .tick line")
        .style("stroke", isDarkMode.value ? "#4B5563" : "#E5E7EB");

      // Add the area
      g.append("path")
        .datum(data)
        .attr("fill", "url(#area-gradient)")
        .attr("d", area);

      // Add the line
      g.append("path")
        .datum(data)
        .attr("fill", "none")
        .attr("stroke", "#8B5CF6")
        .attr("stroke-width", 2)
        .attr("d", line);

      // Add dots
      g.selectAll(".dot")
        .data(data)
        .enter().append("circle")
        .attr("class", "dot")
        .attr("cx", d => xScale(d.time))
        .attr("cy", d => yScale(d.value))
        .attr("r", 3)
        .attr("fill", "#8B5CF6")
        .on("mouseover", function(event, d) {
          // Create tooltip
          const tooltip = container.append("div")
            .attr("class", "tooltip")
            .style("position", "absolute")
            .style("background", isDarkMode.value ? "#1F2937" : "#FFFFFF")
            .style("border", `1px solid ${isDarkMode.value ? "#374151" : "#E5E7EB"}`)
            .style("border-radius", "6px")
            .style("padding", "8px")
            .style("font-size", "12px")
            .style("color", isDarkMode.value ? "#F3F4F6" : "#1F2937")
            .style("box-shadow", "0 4px 6px -1px rgba(0, 0, 0, 0.1)")
            .style("pointer-events", "none")
            .style("z-index", "1000");

          tooltip.html(`
            <div><strong>${d3.timeFormat("%H:%M")(d.time)}</strong></div>
            <div>Partnership: ${d.partnershipId}</div>
            <div>Events: ${d.value.toLocaleString()}</div>
            <div>Revenue: $${d.revenue.toLocaleString()}</div>
            <div>Conversion: ${d.conversionRate.toFixed(2)}%</div>
          `);

          // Position tooltip
          const rect = realtimePerformanceRef.current!.getBoundingClientRect();
          tooltip
            .style("left", (event.clientX - rect.left + 10) + "px")
            .style("top", (event.clientY - rect.top - 10) + "px");
        })
        .on("mouseout", function() {
          container.selectAll(".tooltip").remove();
        });

      // Add chart title
      g.append("text")
        .attr("x", width / 2)
        .attr("y", -5)
        .attr("text-anchor", "middle")
        .style("font-size", "14px")
        .style("font-weight", "600")
        .style("fill", isDarkMode.value ? "#F3F4F6" : "#1F2937")
        .text("Real-time Performance Metrics");

      console.log('Realtime performance chart created successfully with', performanceData.length, 'data points');

      // Update chart status after successful creation
      setChartsInitialized(prev => ({ ...prev, realtimePerformance: true }));

      // Update debug status
      const debugElement = document.getElementById('chart-debug-status');
      if (debugElement) {
        debugElement.textContent = 'All charts initialized successfully';
        debugElement.style.color = '#10B981';
      }
    } catch (error) {
      console.error('Error creating realtime performance chart:', error);

      // Update debug status with error
      const debugElement = document.getElementById('chart-debug-status');
      if (debugElement) {
        debugElement.textContent = `Realtime Performance chart error: ${error instanceof Error ? error.message : String(error)}`;
        debugElement.style.color = '#EF4444';
      }

      // Set chart status to failed
      setChartsInitialized(prev => ({ ...prev, realtimePerformance: false }));
    }
  };



  // Initialize charts on mount and data changes
  useEffect(() => {
    console.log('useEffect triggered with analyticsData:', !!analyticsData);

    // Always try to initialize charts (they have fallback to mock data)
    console.log('Initializing D3 charts...');

    try {
      initializeNetworkTrendsChart();
      initializePartnershipMetricsChart();
      initializeRealtimePerformanceChart();
      console.log('D3 charts initialization complete');

      // Add a visual indicator that charts are initialized
      const debugElement = document.getElementById('chart-debug-status');
      if (debugElement) {
        debugElement.textContent = 'Charts initialized successfully at ' + new Date().toLocaleTimeString();
        debugElement.style.color = 'green';
      }
    } catch (error) {
      console.error('Error initializing charts:', error);

      // Add error indicator
      const debugElement = document.getElementById('chart-debug-status');
      if (debugElement) {
        debugElement.textContent = 'Chart initialization failed: ' + (error instanceof Error ? error.message : String(error));
        debugElement.style.color = 'red';
      }
    }
  }, [analyticsData]);

  // Handle responsive chart resizing
  useEffect(() => {
    const handleResize = () => {
      const container = networkTrendsRef.current?.parentElement;
      if (container) {
        chartDimensions.value = {
          width: Math.max(600, container.clientWidth - 40),
          height: 400
        };
      }
    };

    globalThis.addEventListener('resize', handleResize);
    handleResize(); // Initial sizing

    return () => globalThis.removeEventListener('resize', handleResize);
  }, []);

  // Generate mock network trends data if none available
  const generateMockNetworkTrends = () => {
    const trends = [];
    const baseDate = new Date();
    baseDate.setDate(baseDate.getDate() - 7);

    for (let i = 0; i < 7; i++) {
      const currentDate = new Date(baseDate);
      currentDate.setDate(baseDate.getDate() + i);

      trends.push({
        day: currentDate.toISOString().split('T')[0],
        daily_events: 120 + Math.floor(Math.random() * 80),
        daily_revenue: 2500 + Math.floor(Math.random() * 1500),
        unique_customers: 80 + Math.floor(Math.random() * 40),
        daily_conversion_rate: 2.5 + Math.random() * 2
      });
    }
    return trends;
  };

  // Mock data generation functions
  const generateMockTopPartnerships = () => {
    return [
      { partnership_id: 'partnership-001', total_revenue: 15420, total_events: 342 },
      { partnership_id: 'partnership-002', total_revenue: 12850, total_events: 298 },
      { partnership_id: 'partnership-003', total_revenue: 9680, total_events: 215 },
      { partnership_id: 'partnership-004', total_revenue: 7320, total_events: 189 },
      { partnership_id: 'partnership-005', total_revenue: 5940, total_events: 156 }
    ];
  };

  const generateMockRealtimePerformance = () => {
    const performance = [];
    const baseDate = new Date();
    baseDate.setHours(baseDate.getHours() - 2);

    for (let i = 0; i < 8; i++) { // 8 intervals of 15 minutes = 2 hours
      const currentDate = new Date(baseDate);
      currentDate.setMinutes(baseDate.getMinutes() + (i * 15));

      performance.push({
        quarter_hour: currentDate.toISOString(),
        partnership_id: 'realtime-aggregate',
        events_15min: 25 + Math.floor(Math.random() * 20),
        revenue_15min: 800 + Math.floor(Math.random() * 400),
        conversion_rate_15min: 2.8 + Math.random() * 1.5
      });
    }
    return performance;
  };

  // All old chart implementations removed - using simplified useCallback versions above

  return (
    <div class="d3-marketplace-analytics-dashboard" role="main" aria-label="Marketplace Analytics Dashboard">
      {/* Chart Status Indicator */}
      <div class="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
        <div class="flex items-center space-x-4 text-sm">
          <span class="font-medium text-blue-900 dark:text-blue-100">Chart Status:</span>
          <div class="flex space-x-3">
            <span class={`flex items-center ${chartsInitialized.networkTrends ? 'text-green-600' : 'text-gray-500'}`}>
              <span class={`w-2 h-2 rounded-full mr-1 ${chartsInitialized.networkTrends ? 'bg-green-500' : 'bg-gray-400'}`}></span>
              Network Trends
            </span>
            <span class={`flex items-center ${chartsInitialized.partnershipMetrics ? 'text-green-600' : 'text-gray-500'}`}>
              <span class={`w-2 h-2 rounded-full mr-1 ${chartsInitialized.partnershipMetrics ? 'bg-green-500' : 'bg-gray-400'}`}></span>
              Top Partnerships
            </span>
            <span class={`flex items-center ${chartsInitialized.realtimePerformance ? 'text-green-600' : 'text-gray-500'}`}>
              <span class={`w-2 h-2 rounded-full mr-1 ${chartsInitialized.realtimePerformance ? 'bg-green-500' : 'bg-gray-400'}`}></span>
              Real-time Performance
            </span>
          </div>
        </div>
      </div>

      {/* Debug Status Indicator */}
      <div class="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
        <div class="flex items-center space-x-4 text-sm">
          <span class="font-medium text-yellow-900 dark:text-yellow-100">Debug Status:</span>
          <span id="chart-debug-status" class="text-gray-600 dark:text-gray-300">
            Waiting for chart initialization...
          </span>
        </div>
      </div>

      {/* Dashboard Controls */}
      <div class="mb-6 space-y-4">
        {/* Primary Controls */}
        <div class="flex flex-wrap items-center justify-between gap-4">
          <div class="flex items-center space-x-4">
            <label class="flex flex-col">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Time Range</span>
              <select
                value={selectedTimeFrame}
                onChange={(e) => {
                  setSelectedTimeFrame(e.currentTarget.value);
                  onTimeFrameChange?.(e.currentTarget.value);
                }}
                class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                aria-label="Select time range for analytics data"
              >
                <option value="7d">Last 7 Days</option>
                <option value="30d">Last 30 Days</option>
                <option value="90d">Last 90 Days</option>
              </select>
            </label>

            <label class="flex flex-col">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Metric</span>
              <select
                value={selectedMetric}
                onChange={(e) => {
                  setSelectedMetric(e.currentTarget.value);
                  onMetricChange?.(e.currentTarget.value);
                }}
                class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                aria-label="Select metric to display in charts"
              >
                <option value="revenue">Revenue</option>
                <option value="events">Events</option>
                <option value="conversions">Conversions</option>
              </select>
            </label>
          </div>

          <div class="flex items-center space-x-3">
            {/* Real-time Status Indicator */}
            <div class="flex items-center space-x-2">
              <div class={`w-3 h-3 rounded-full ${
                connectionStatus === 'connected' ? 'bg-green-500' :
                connectionStatus === 'connecting' ? 'bg-yellow-500' : 'bg-red-500'
              }`} aria-hidden="true"></div>
              <span class="text-sm text-gray-600 dark:text-gray-300">
                {connectionStatus === 'connected' ? 'Live' :
                 connectionStatus === 'connecting' ? 'Connecting...' : 'Offline'}
              </span>
              {lastUpdate && (
                <span class="text-xs text-gray-500 dark:text-gray-400">
                  Last update: {lastUpdate}
                </span>
              )}
            </div>

            <label class="flex items-center">
              <input
                type="checkbox"
                checked={isRealTimeEnabled}
                onChange={(e) => setIsRealTimeEnabled(e.currentTarget.checked)}
                class="mr-2 focus:ring-2 focus:ring-blue-500"
                aria-describedby="realtime-help"
              />
              <span class="text-sm text-gray-700 dark:text-gray-300">Real-time Updates</span>
              <span id="realtime-help" class="sr-only">
                Enable or disable real-time data streaming for live analytics updates
              </span>
            </label>

            <button
              type="button"
              onClick={() => onDataExport?.(analyticsData, 'csv')}
              class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm font-medium transition-colors focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              aria-label="Export analytics data as CSV file"
            >
              Export Data
            </button>
          </div>
        </div>

        {/* Enhanced Cohort Filtering Controls */}
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between mb-3">
            <h3 class="text-sm font-semibold text-gray-900 dark:text-white">Cohort Analysis Filters</h3>
            <label class="flex items-center">
              <input
                type="checkbox"
                checked={showComparison}
                onChange={(e) => setShowComparison(e.currentTarget.checked)}
                class="mr-2 focus:ring-2 focus:ring-blue-500"
              />
              <span class="text-sm text-gray-700 dark:text-gray-300">Show Comparison View</span>
            </label>
          </div>

          <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
            <label class="flex flex-col">
              <span class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Cohort Type</span>
              <select
                value={cohortFilters.cohortType}
                onChange={(e) => setCohortFilters(prev => ({ ...prev, cohortType: e.currentTarget.value as CohortFilters['cohortType'] }))}
                class="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-1 focus:ring-blue-500"
              >
                <option value="all">All Types</option>
                <option value="organic">Organic</option>
                <option value="partnership_acquisition">Partnership Acquisition</option>
                <option value="cross_tenant_collaboration">Cross-Tenant Collaboration</option>
                <option value="referral_program">Referral Program</option>
                <option value="joint_marketing_campaign">Joint Marketing</option>
              </select>
            </label>

            <label class="flex flex-col">
              <span class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Partnership Type</span>
              <select
                value={cohortFilters.partnershipType}
                onChange={(e) => setCohortFilters(prev => ({ ...prev, partnershipType: e.currentTarget.value as CohortFilters['partnershipType'] }))}
                class="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-1 focus:ring-blue-500"
              >
                <option value="all">All Partnerships</option>
                <option value="strategic">Strategic</option>
                <option value="affiliate">Affiliate</option>
                <option value="integration">Integration</option>
              </select>
            </label>

            <label class="flex flex-col">
              <span class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Acquisition Channel</span>
              <select
                value={cohortFilters.acquisitionChannel}
                onChange={(e) => setCohortFilters(prev => ({ ...prev, acquisitionChannel: e.currentTarget.value as CohortFilters['acquisitionChannel'] }))}
                class="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-1 focus:ring-blue-500"
              >
                <option value="all">All Channels</option>
                <option value="direct">Direct</option>
                <option value="referral">Referral</option>
                <option value="collaboration">Collaboration</option>
                <option value="cross_promotion">Cross Promotion</option>
              </select>
            </label>

            <label class="flex flex-col">
              <span class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Analysis Period</span>
              <select
                value={cohortFilters.timeRange}
                onChange={(e) => setCohortFilters(prev => ({ ...prev, timeRange: e.currentTarget.value as CohortFilters['timeRange'] }))}
                class="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-1 focus:ring-blue-500"
              >
                <option value="30d">30 Days</option>
                <option value="90d">90 Days</option>
                <option value="180d">180 Days</option>
                <option value="1y">1 Year</option>
              </select>
            </label>

            <label class="flex flex-col">
              <span class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Comparison Mode</span>
              <select
                value={cohortFilters.comparisonMode}
                onChange={(e) => setCohortFilters(prev => ({ ...prev, comparisonMode: e.currentTarget.value as CohortFilters['comparisonMode'] }))}
                class="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-1 focus:ring-blue-500"
              >
                <option value="absolute">Absolute Values</option>
                <option value="vs_organic">vs Organic</option>
                <option value="vs_average">vs Average</option>
                <option value="vs_top_quartile">vs Top Quartile</option>
              </select>
            </label>
          </div>
        </div>
      </div>

      {/* Charts Grid */}
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6" role="region" aria-label="Analytics Charts">
        {/* Network Trends Chart */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Network Trends - {selectedMetric === 'revenue' ? 'Revenue' : 'Events'}
          </h3>
          <div
            ref={networkTrendsRef}
            class="w-full"
            role="img"
            aria-label={`Network trends chart showing ${selectedMetric} over ${selectedTimeFrame}`}
            tabIndex={0}
          ></div>
        </div>

        {/* Partnership Metrics Chart */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Top Partnerships by Revenue
          </h3>
          <div
            ref={partnershipMetricsRef}
            class="w-full"
            role="img"
            aria-label="Bar chart showing top partnerships ranked by revenue performance"
            tabIndex={0}
          ></div>
        </div>

        {/* Real-time Performance Chart */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Real-time Performance
            {isRealTimeEnabled && connectionStatus === 'connected' && (
              <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                Live
              </span>
            )}
          </h3>
          <div
            ref={realtimePerformanceRef}
            class="w-full"
            role="img"
            aria-label="Real-time performance area chart showing live marketplace activity in 15-minute intervals"
            tabIndex={0}
          ></div>
        </div>

        {/* Cohort Analysis Chart */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Cohort Analysis
            {analyticsData.advanced_analytics?.cohort_analysis && (
              <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                Advanced
              </span>
            )}
          </h3>
          <div
            ref={cohortAnalysisRef}
            class="w-full"
            role="img"
            aria-label="Cohort analysis visualization showing customer retention patterns over time"
            tabIndex={0}
          ></div>
        </div>
      </div>

      {/* Enhanced Analytics Status */}
      {analyticsData.advanced_analytics && (
        <div class="mt-6 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Marketplace Analytics Status</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div class="text-center">
              <div class="font-medium text-blue-600 dark:text-blue-400">
                {analyticsData.advanced_analytics?.cohort_analysis ? '✓' : '✗'}
              </div>
              <div class="text-gray-600 dark:text-gray-300">Cohort Analysis</div>
            </div>
            <div class="text-center">
              <div class="font-medium text-green-600 dark:text-green-400">
                {analyticsData.advanced_analytics?.clv_analysis ? '✓' : '✗'}
              </div>
              <div class="text-gray-600 dark:text-gray-300">CLV Analysis</div>
            </div>
            <div class="text-center">
              <div class="font-medium text-purple-600 dark:text-purple-400">
                {analyticsData.advanced_analytics?.funnel_analysis ? '✓' : '✗'}
              </div>
              <div class="text-gray-600 dark:text-gray-300">Funnel Analysis</div>
            </div>
            <div class="text-center">
              <div class="font-medium text-orange-600 dark:text-orange-400">
                {analyticsData.advanced_analytics?.predictive_analysis ? '✓' : '✗'}
              </div>
              <div class="text-gray-600 dark:text-gray-300">Predictive Analytics</div>
            </div>
          </div>

          {/* Partnership Performance Summary */}
          {analyticsData.advanced_analytics?.cohort_analysis?.partnership_cohorts && (
            <div class="mt-4 pt-4 border-t border-blue-200 dark:border-blue-700">
              <div class="flex items-center justify-between text-sm">
                <span class="text-gray-600 dark:text-gray-300">
                  Partnership Performance vs Organic:
                  <span class="font-semibold text-green-600 dark:text-green-400 ml-1">
                    +{(((analyticsData.advanced_analytics?.cohort_analysis?.overview.partnershipRetentionAverage || 0.72) / (analyticsData.advanced_analytics?.cohort_analysis?.overview.organicRetentionBaseline || 0.58) - 1) * 100).toFixed(1)}%
                  </span>
                </span>
                <span class="text-gray-600 dark:text-gray-300">
                  Active Partnerships:
                  <span class="font-semibold text-blue-600 dark:text-blue-400 ml-1">
                    {analyticsData.advanced_analytics?.cohort_analysis?.partnership_cohorts?.length || 0}
                  </span>
                </span>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
