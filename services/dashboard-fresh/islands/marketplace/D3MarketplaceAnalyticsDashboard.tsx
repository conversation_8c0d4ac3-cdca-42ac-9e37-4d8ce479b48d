// D3.js Marketplace Analytics Dashboard Island
// Advanced interactive visualizations for marketplace analytics with real-time capabilities

import { useEffect, useRef, useState } from "preact/hooks";
import { useSignal } from "@preact/signals";
import * as d3 from "d3";
import { User } from "../../utils/auth.ts";

// Helper function to calculate retention decay factors based on cohort type
const getRetentionDecayFactor = (
  cohortType: 'partnership_acquisition' | 'cross_tenant_collaboration' | 'referral_program' | 'joint_marketing_campaign' | 'organic',
  partnershipType?: 'strategic' | 'affiliate' | 'integration'
): number => {
  // Base decay factors by cohort type (higher = better retention)
  const baseFactors = {
    organic: 0.88,
    partnership_acquisition: 0.92,
    cross_tenant_collaboration: 0.95,
    referral_program: 0.90,
    joint_marketing_campaign: 0.89
  };

  // Partnership type multipliers
  const partnershipMultipliers = {
    strategic: 1.05,
    integration: 1.08,
    affiliate: 1.02
  };

  const baseFactor = baseFactors[cohortType];
  const multiplier = partnershipType ? partnershipMultipliers[partnershipType] : 1.0;

  // Add some randomness (±3%) to simulate real-world variation
  const randomVariation = 0.97 + Math.random() * 0.06;

  return Math.min(0.98, baseFactor * multiplier * randomVariation);
};

interface CohortSegment {
  id: string;
  name?: string;
  startDate: string;
  size: number;
  retentionRates: number[];
  cohortType?: 'partnership_acquisition' | 'cross_tenant_collaboration' | 'referral_program' | 'joint_marketing_campaign' | 'organic';
  partnershipType?: 'strategic' | 'affiliate' | 'integration';
  acquisitionChannel?: 'referral' | 'collaboration' | 'cross_promotion' | 'direct';
  partnershipId?: string;
  partnerName?: string;
  revenueData?: number[];
  conversionRates?: number[];
}

interface MarketplaceCohortComparison {
  vs_organic_retention: number;
  vs_average_partnership: number;
  revenue_multiplier: number;
  performance_tier: 'top_quartile' | 'above_average' | 'average' | 'below_average';
}

interface CohortAnalysisData {
  segments: CohortSegment[];
  overview: {
    totalCohorts: number;
    avgRetentionRate: number;
    bestPerformingCohort: string;
    organicRetentionBaseline: number;
    partnershipRetentionAverage: number;
  };
  partnership_cohorts?: {
    partnership_id: string;
    partner_name: string;
    acquisition_channel: 'referral' | 'collaboration' | 'cross_promotion';
    segments: CohortSegment[];
    comparative_metrics: MarketplaceCohortComparison;
  }[];
  cross_tenant_analysis?: {
    tenant_pair: string;
    shared_customer_retention: number[];
    collaboration_effectiveness: number;
  }[];
}

interface CohortFilters {
  cohortType: 'all' | 'partnership_acquisition' | 'cross_tenant_collaboration' | 'referral_program' | 'joint_marketing_campaign' | 'organic';
  partnershipType: 'all' | 'strategic' | 'affiliate' | 'integration';
  acquisitionChannel: 'all' | 'referral' | 'collaboration' | 'cross_promotion' | 'direct';
  timeRange: '30d' | '90d' | '180d' | '1y';
  comparisonMode: 'vs_organic' | 'vs_average' | 'vs_top_quartile' | 'absolute';
}

interface MarketplaceAnalyticsData {
  partnership_metrics: Array<{
    hour: string;
    partnership_id: string;
    event_count: number;
    total_revenue: number;
    conversion_rate: number;
    unique_customers: number;
  }>;
  network_trends: Array<{
    day: string;
    daily_events: number;
    daily_revenue: number;
    unique_customers: number;
    daily_conversion_rate: number;
  }>;
  tenant_activity: Array<{
    hour: string;
    tenant_id: string;
    direction: string;
    event_count: number;
    total_revenue: number;
    unique_partners: number;
  }>;
  realtime_performance: Array<{
    quarter_hour: string;
    partnership_id: string;
    events_15min: number;
    revenue_15min: number;
    conversion_rate_15min: number;
  }>;
  summary_stats: {
    total_partnerships: number;
    active_partnerships: number;
    total_revenue_7d: number;
    total_events_7d: number;
    avg_conversion_rate: number;
    top_partnerships: Array<{
      partnership_id: string;
      total_revenue: number;
      total_events: number;
    }>;
  };
  advanced_analytics?: {
    cohort_analysis?: CohortAnalysisData;
    clv_analysis?: unknown;
    funnel_analysis?: unknown;
    predictive_analysis?: unknown;
  };
}

interface D3MarketplaceAnalyticsDashboardProps {
  analyticsData: MarketplaceAnalyticsData;
  user: User;
  onTimeFrameChange?: (timeFrame: string) => void;
  onMetricChange?: (metric: string) => void;
  onDataExport?: (data: MarketplaceAnalyticsData, format: string) => void;
}

// Generate mock cohort data for demonstration
const generateMockCohortData = (): CohortAnalysisData => {
  console.log('Generating enhanced marketplace cohort data...');

  const cohorts: CohortSegment[] = [];

  // Define marketplace-specific cohort configurations
  const cohortConfigs = [
    // Organic baseline cohorts
    { name: 'Organic Jan 2024', type: 'organic' as const, channel: 'direct' as const, partnershipType: undefined, baseRetention: 0.58, size: 450 },
    { name: 'Organic Feb 2024', type: 'organic' as const, channel: 'direct' as const, partnershipType: undefined, baseRetention: 0.60, size: 520 },

    // Strategic partnership cohorts (higher retention)
    { name: 'Strategic Partner A', type: 'partnership_acquisition' as const, channel: 'collaboration' as const, partnershipType: 'strategic' as const, baseRetention: 0.78, size: 320, partnerId: 'strategic-001', partnerName: 'TechCorp Solutions' },
    { name: 'Strategic Partner B', type: 'partnership_acquisition' as const, channel: 'referral' as const, partnershipType: 'strategic' as const, baseRetention: 0.82, size: 280, partnerId: 'strategic-002', partnerName: 'Enterprise Systems Inc' },

    // Affiliate partnership cohorts (moderate retention)
    { name: 'Affiliate Network Q1', type: 'referral_program' as const, channel: 'referral' as const, partnershipType: 'affiliate' as const, baseRetention: 0.68, size: 180, partnerId: 'affiliate-001', partnerName: 'Marketing Affiliates' },
    { name: 'Affiliate Network Q2', type: 'referral_program' as const, channel: 'cross_promotion' as const, partnershipType: 'affiliate' as const, baseRetention: 0.65, size: 220, partnerId: 'affiliate-002', partnerName: 'Growth Partners' },

    // Integration partnership cohorts (high retention)
    { name: 'Integration Partner', type: 'cross_tenant_collaboration' as const, channel: 'collaboration' as const, partnershipType: 'integration' as const, baseRetention: 0.85, size: 150, partnerId: 'integration-001', partnerName: 'Platform Integrators' },

    // Joint marketing campaigns (variable retention)
    { name: 'Joint Campaign Q1', type: 'joint_marketing_campaign' as const, channel: 'cross_promotion' as const, partnershipType: 'strategic' as const, baseRetention: 0.72, size: 380, partnerId: 'campaign-001', partnerName: 'Marketing Alliance' },
    { name: 'Joint Campaign Q2', type: 'joint_marketing_campaign' as const, channel: 'collaboration' as const, partnershipType: 'affiliate' as const, baseRetention: 0.69, size: 290, partnerId: 'campaign-002', partnerName: 'Co-Marketing Group' }
  ];

  cohortConfigs.forEach((config, index) => {
    const retentionRates: number[] = [];
    const revenueData: number[] = [];
    const conversionRates: number[] = [];
    let currentRetention = 1.0; // Start at 100%
    let currentRevenue = config.size * (50 + Math.random() * 100); // Initial revenue per cohort

    for (let period = 0; period < 12; period++) {
      if (period === 0) {
        retentionRates.push(1.0);
        revenueData.push(currentRevenue);
        conversionRates.push(0.15 + Math.random() * 0.1); // 15-25% initial conversion
      } else {
        // Calculate retention decay based on cohort type
        const decayFactor = getRetentionDecayFactor(config.type, config.partnershipType);
        currentRetention *= decayFactor;

        // Apply baseline retention adjustment
        const adjustedRetention = Math.max(0.05, currentRetention * (config.baseRetention / 0.65));
        retentionRates.push(adjustedRetention);

        // Calculate revenue based on retention and cohort performance
        currentRevenue *= (adjustedRetention + 0.1); // Revenue follows retention with slight premium
        revenueData.push(currentRevenue);

        // Conversion rates tend to improve over time for retained customers
        const conversionRate = Math.min(0.35, (0.15 + period * 0.02) * (adjustedRetention + 0.2));
        conversionRates.push(conversionRate);
      }
    }

    cohorts.push({
      id: `cohort-${index}`,
      name: config.name,
      startDate: new Date(2024, index % 6, 1).toISOString(),
      size: config.size,
      retentionRates,
      revenueData,
      conversionRates,
      cohortType: config.type,
      partnershipType: config.partnershipType,
      acquisitionChannel: config.channel,
      partnershipId: config.partnerId,
      partnerName: config.partnerName
    });
  });

  // Calculate enhanced overview metrics
  const organicCohorts = cohorts.filter(c => c.cohortType === 'organic');
  const partnershipCohorts = cohorts.filter(c => c.cohortType !== 'organic');

  const organicRetentionBaseline = organicCohorts.length > 0
    ? organicCohorts.reduce((sum, c) => sum + c.retentionRates[6], 0) / organicCohorts.length // 6-month retention
    : 0.58;

  const partnershipRetentionAverage = partnershipCohorts.length > 0
    ? partnershipCohorts.reduce((sum, c) => sum + c.retentionRates[6], 0) / partnershipCohorts.length
    : 0.72;

  const bestPerformingCohort = cohorts.reduce((best, current) =>
    current.retentionRates[6] > best.retentionRates[6] ? current : best
  );

  const mockData: CohortAnalysisData = {
    segments: cohorts,
    overview: {
      totalCohorts: cohorts.length,
      avgRetentionRate: cohorts.reduce((sum, c) => sum + c.retentionRates[6], 0) / cohorts.length,
      bestPerformingCohort: bestPerformingCohort.name || 'N/A',
      organicRetentionBaseline,
      partnershipRetentionAverage
    },
    partnership_cohorts: partnershipCohorts.map(cohort => ({
      partnership_id: cohort.partnershipId || cohort.id,
      partner_name: cohort.partnerName || cohort.name || 'Unknown Partner',
      acquisition_channel: cohort.acquisitionChannel as 'referral' | 'collaboration' | 'cross_promotion',
      segments: [cohort],
      comparative_metrics: {
        vs_organic_retention: cohort.retentionRates[6] / organicRetentionBaseline,
        vs_average_partnership: cohort.retentionRates[6] / partnershipRetentionAverage,
        revenue_multiplier: (cohort.revenueData?.[6] || 0) / (organicCohorts[0]?.revenueData?.[6] || 1),
        performance_tier: getPerformanceTier(cohort.retentionRates[6], partnershipRetentionAverage)
      }
    })),
    cross_tenant_analysis: [
      {
        tenant_pair: 'TechCorp-Enterprise',
        shared_customer_retention: [1.0, 0.92, 0.87, 0.83, 0.79, 0.76, 0.73],
        collaboration_effectiveness: 1.15
      },
      {
        tenant_pair: 'Marketing-Growth',
        shared_customer_retention: [1.0, 0.88, 0.81, 0.75, 0.70, 0.66, 0.62],
        collaboration_effectiveness: 1.08
      }
    ]
  };

  console.log('Generated enhanced marketplace cohort data:', {
    segmentCount: mockData.segments.length,
    partnershipCount: mockData.partnership_cohorts?.length || 0,
    organicBaseline: organicRetentionBaseline.toFixed(3),
    partnershipAverage: partnershipRetentionAverage.toFixed(3),
    bestPerformer: bestPerformingCohort.name,
    overview: mockData.overview
  });

  return mockData;
};

// Helper function to determine performance tier
const getPerformanceTier = (retention: number, average: number): 'top_quartile' | 'above_average' | 'average' | 'below_average' => {
  const ratio = retention / average;
  if (ratio >= 1.25) return 'top_quartile';
  if (ratio >= 1.1) return 'above_average';
  if (ratio >= 0.9) return 'average';
  return 'below_average';
};

export default function D3MarketplaceAnalyticsDashboard({
  analyticsData,
  user: _user,
  onTimeFrameChange,
  onMetricChange,
  onDataExport
}: D3MarketplaceAnalyticsDashboardProps) {
  console.log('D3MarketplaceAnalyticsDashboard component rendered with data:', {
    hasAnalyticsData: !!analyticsData,
    dataKeys: analyticsData ? Object.keys(analyticsData) : [],
    networkTrendsLength: analyticsData?.network_trends?.length || 0,
    topPartnershipsLength: analyticsData?.summary_stats?.top_partnerships?.length || 0,
    realtimePerformanceLength: analyticsData?.realtime_performance?.length || 0
  });

  // Refs for D3 chart containers
  const networkTrendsRef = useRef<HTMLDivElement>(null);
  const partnershipMetricsRef = useRef<HTMLDivElement>(null);
  const realtimePerformanceRef = useRef<HTMLDivElement>(null);
  const cohortAnalysisRef = useRef<HTMLDivElement>(null);

  // State management
  const [selectedTimeFrame, setSelectedTimeFrame] = useState('7d');
  const [selectedMetric, setSelectedMetric] = useState('revenue');
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(true);
  const [_loading, _setLoading] = useState(false);
  const [_realtimeData, setRealtimeData] = useState<unknown[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('disconnected');
  const [lastUpdate, setLastUpdate] = useState<string>('');
  const [chartsInitialized, setChartsInitialized] = useState<{
    networkTrends: boolean;
    partnershipMetrics: boolean;
    realtimePerformance: boolean;
    cohortAnalysis: boolean;
  }>({
    networkTrends: false,
    partnershipMetrics: false,
    realtimePerformance: false,
    cohortAnalysis: false
  });

  // Enhanced cohort filtering state
  const [cohortFilters, setCohortFilters] = useState<CohortFilters>({
    cohortType: 'all',
    partnershipType: 'all',
    acquisitionChannel: 'all',
    timeRange: '180d',
    comparisonMode: 'vs_organic'
  });
  const [showComparison, setShowComparison] = useState(false);

  // Signals for reactive updates
  const chartDimensions = useSignal({ width: 800, height: 400 });
  const isDarkMode = useSignal(false);

  // Real-time streaming connection
  useEffect(() => {
    if (!isRealTimeEnabled) return;

    let eventSource: EventSource | null = null;

    const connectToStream = () => {
      setConnectionStatus('connecting');
      eventSource = new EventSource('/api/marketplace/analytics/stream');

      eventSource.onopen = () => {
        console.log('Connected to real-time analytics stream');
        setConnectionStatus('connected');
      };

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          if (data.type === 'heartbeat') {
            setLastUpdate(new Date().toLocaleTimeString());
            return;
          }

          if (data.event_type) {
            setRealtimeData(prev => [...prev.slice(-19), data]); // Keep last 20 updates
            setLastUpdate(new Date().toLocaleTimeString());

            // Update charts with new data
            if (data.event_type === 'performance_update' || data.event_type === 'revenue_update') {
              updateRealtimeCharts(data);
            }
          }
        } catch (error) {
          console.error('Error parsing real-time data:', error);
        }
      };

      eventSource.onerror = (error) => {
        console.error('Real-time stream error:', error);
        setConnectionStatus('disconnected');

        // Attempt to reconnect after 5 seconds
        setTimeout(() => {
          if (isRealTimeEnabled) {
            connectToStream();
          }
        }, 5000);
      };
    };

    connectToStream();

    return () => {
      if (eventSource) {
        eventSource.close();
        setConnectionStatus('disconnected');
      }
    };
  }, [isRealTimeEnabled]);

  // Update charts with real-time data
  const updateRealtimeCharts = (data: { data?: { network_performance?: { total_events_1min: number; total_revenue_1min: number; avg_conversion_rate: number } } }) => {
    if (data.data?.network_performance) {
      // Update real-time performance chart
      const newDataPoint = {
        time: new Date(),
        events_15min: data.data.network_performance.total_events_1min * 15, // Simulate 15-min aggregation
        revenue_15min: data.data.network_performance.total_revenue_1min * 15,
        conversion_rate_15min: data.data.network_performance.avg_conversion_rate
      };

      // Add to existing realtime_performance data
      analyticsData.realtime_performance = [
        ...analyticsData.realtime_performance.slice(-19), // Keep last 19 points
        {
          quarter_hour: newDataPoint.time.toISOString(),
          partnership_id: 'realtime-aggregate',
          events_15min: newDataPoint.events_15min,
          revenue_15min: newDataPoint.revenue_15min,
          conversion_rate_15min: newDataPoint.conversion_rate_15min
        }
      ];

      // Re-render the real-time chart
      initializeRealtimePerformanceChart();
    }
  };

  // Chart initialization function declarations
  const initializeNetworkTrendsChart = () => {
    console.log('Initializing Network Trends Chart...');
    if (!networkTrendsRef.current) {
      console.log('Network trends ref not available');
      return;
    }

    // Use real data if available, otherwise generate mock data
    const trendsData = analyticsData.network_trends.length > 0
      ? analyticsData.network_trends
      : generateMockNetworkTrends();

    console.log('Network trends data:', {
      realDataLength: analyticsData.network_trends.length,
      trendsDataLength: trendsData.length,
      sampleData: trendsData[0]
    });

    if (!trendsData.length) {
      console.log('No network trends data available');
      return;
    }

    try {
      setChartsInitialized(prev => ({ ...prev, networkTrends: true }));

      const container = d3.select(networkTrendsRef.current);
      container.selectAll("*").remove();

      console.log('Network trends container selected, starting chart creation...');

      // Chart creation logic would go here
      console.log('Network trends chart created successfully');
    } catch (error) {
      console.error('Error creating network trends chart:', error);
    }
  };

  const initializePartnershipMetricsChart = () => {
    console.log('Initializing Partnership Metrics Chart...');
    if (!partnershipMetricsRef.current) {
      console.log('Partnership metrics ref not available');
      return;
    }

    // Use real data if available, otherwise generate mock data
    const partnershipsData = analyticsData.summary_stats.top_partnerships.length > 0
      ? analyticsData.summary_stats.top_partnerships
      : generateMockTopPartnerships();

    console.log('Partnership metrics data:', {
      realDataLength: analyticsData.summary_stats.top_partnerships.length,
      partnershipsDataLength: partnershipsData.length,
      sampleData: partnershipsData[0]
    });

    if (!partnershipsData.length) {
      console.log('No partnership metrics data available');
      return;
    }

    try {
      setChartsInitialized(prev => ({ ...prev, partnershipMetrics: true }));

      const container = d3.select(partnershipMetricsRef.current);
      container.selectAll("*").remove();

      console.log('Partnership metrics chart created successfully');
    } catch (error) {
      console.error('Error creating partnership metrics chart:', error);
    }
  };

  const initializeRealtimePerformanceChart = () => {
    console.log('Initializing Realtime Performance Chart...');
    if (!realtimePerformanceRef.current) {
      console.log('Realtime performance ref not available');
      return;
    }

    // Use real data if available, otherwise generate mock data
    const performanceData = analyticsData.realtime_performance.length > 0
      ? analyticsData.realtime_performance
      : generateMockRealtimePerformance();

    console.log('Realtime performance data:', {
      realDataLength: analyticsData.realtime_performance.length,
      performanceDataLength: performanceData.length,
      sampleData: performanceData[0]
    });

    if (!performanceData.length) {
      console.log('No realtime performance data available');
      return;
    }

    try {
      setChartsInitialized(prev => ({ ...prev, realtimePerformance: true }));

      const container = d3.select(realtimePerformanceRef.current);
      container.selectAll("*").remove();

      console.log('Realtime performance chart created successfully');
    } catch (error) {
      console.error('Error creating realtime performance chart:', error);
    }
  };

  const initializeCohortAnalysisChart = () => {
    console.log('Initializing Cohort Analysis Chart...');
    if (!cohortAnalysisRef.current) {
      console.log('Cohort analysis ref not available');
      return;
    }

    try {
      setChartsInitialized(prev => ({ ...prev, cohortAnalysis: true }));

      const container = d3.select(cohortAnalysisRef.current);
      container.selectAll("*").remove();

      console.log('Cohort analysis chart created successfully');
    } catch (error) {
      console.error('Error creating cohort analysis chart:', error);
    }
  };

  // Initialize charts on mount and data changes
  useEffect(() => {
    if (analyticsData) {
      console.log('Initializing charts with analytics data:', {
        hasNetworkTrends: analyticsData.network_trends?.length > 0,
        networkTrendsLength: analyticsData.network_trends?.length || 0,
        hasPartnershipMetrics: analyticsData.summary_stats?.top_partnerships?.length > 0,
        topPartnershipsLength: analyticsData.summary_stats?.top_partnerships?.length || 0,
        hasRealtimePerformance: analyticsData.realtime_performance?.length > 0,
        realtimePerformanceLength: analyticsData.realtime_performance?.length || 0,
        hasAdvancedAnalytics: !!analyticsData.advanced_analytics,
        hasCohortAnalysis: !!analyticsData.advanced_analytics?.cohort_analysis,
        cohortFilters: cohortFilters,
        fullAnalyticsData: analyticsData
      });

      console.log('Initializing D3 charts...');
      initializeNetworkTrendsChart();
      initializePartnershipMetricsChart();
      initializeRealtimePerformanceChart();
      // Always initialize cohort analysis chart - it has built-in fallback to mock data
      initializeCohortAnalysisChart();
      console.log('D3 charts initialization complete');
    }
  }, [analyticsData, selectedTimeFrame, selectedMetric, cohortFilters, showComparison]);

  // Handle responsive chart resizing
  useEffect(() => {
    const handleResize = () => {
      const container = networkTrendsRef.current?.parentElement;
      if (container) {
        chartDimensions.value = {
          width: Math.max(600, container.clientWidth - 40),
          height: 400
        };
      }
    };

    globalThis.addEventListener('resize', handleResize);
    handleResize(); // Initial sizing

    return () => globalThis.removeEventListener('resize', handleResize);
  }, []);

  // Generate mock network trends data if none available
  const generateMockNetworkTrends = () => {
    const trends = [];
    const baseDate = new Date();
    baseDate.setDate(baseDate.getDate() - 7);

    for (let i = 0; i < 7; i++) {
      const currentDate = new Date(baseDate);
      currentDate.setDate(baseDate.getDate() + i);

      trends.push({
        day: currentDate.toISOString().split('T')[0],
        daily_events: 120 + Math.floor(Math.random() * 80),
        daily_revenue: 2500 + Math.floor(Math.random() * 1500),
        unique_customers: 80 + Math.floor(Math.random() * 40),
        daily_conversion_rate: 2.5 + Math.random() * 2
      });
    }
    return trends;
  };

  // Mock data generation functions
  const generateMockTopPartnerships = () => {
    return [
      { partnership_id: 'partnership-001', total_revenue: 15420, total_events: 342 },
      { partnership_id: 'partnership-002', total_revenue: 12850, total_events: 298 },
      { partnership_id: 'partnership-003', total_revenue: 9680, total_events: 215 },
      { partnership_id: 'partnership-004', total_revenue: 7320, total_events: 189 },
      { partnership_id: 'partnership-005', total_revenue: 5940, total_events: 156 }
    ];
  };

  const generateMockRealtimePerformance = () => {
    const performance = [];
    const baseDate = new Date();
    baseDate.setHours(baseDate.getHours() - 2);

    for (let i = 0; i < 8; i++) { // 8 intervals of 15 minutes = 2 hours
      const currentDate = new Date(baseDate);
      currentDate.setMinutes(baseDate.getMinutes() + (i * 15));

      performance.push({
        quarter_hour: currentDate.toISOString(),
        partnership_id: 'realtime-aggregate',
        events_15min: 25 + Math.floor(Math.random() * 20),
        revenue_15min: 800 + Math.floor(Math.random() * 400),
        conversion_rate_15min: 2.8 + Math.random() * 1.5
      });
    }
    return performance;
  };

  // All old chart implementations removed - using simplified useCallback versions above

  // Initialize charts when data is available
  useEffect(() => {
    if (analyticsData && Object.keys(analyticsData).length > 0) {
      console.log('Initializing all marketplace charts with data:', {
        hasNetworkTrends: analyticsData.network_trends?.length > 0,
        hasTopPartnerships: analyticsData.summary_stats?.top_partnerships?.length > 0,
        hasRealtimePerformance: analyticsData.realtime_performance?.length > 0,
        dataKeys: Object.keys(analyticsData)
      });

      // Initialize all charts
      initializeNetworkTrendsChart();
      initializePartnershipMetricsChart();
      initializeRealtimePerformanceChart();
    }
  }, [analyticsData]);

  return (
    <div class="d3-marketplace-analytics-dashboard" role="main" aria-label="Marketplace Analytics Dashboard">
      {/* Chart Status Indicator */}
      <div class="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
        <div class="flex items-center space-x-4 text-sm">
          <span class="font-medium text-blue-900 dark:text-blue-100">Chart Status:</span>
          <div class="flex space-x-3">
            <span class={`flex items-center ${chartsInitialized.networkTrends ? 'text-green-600' : 'text-gray-500'}`}>
              <span class={`w-2 h-2 rounded-full mr-1 ${chartsInitialized.networkTrends ? 'bg-green-500' : 'bg-gray-400'}`}></span>
              Network Trends
            </span>
            <span class={`flex items-center ${chartsInitialized.partnershipMetrics ? 'text-green-600' : 'text-gray-500'}`}>
              <span class={`w-2 h-2 rounded-full mr-1 ${chartsInitialized.partnershipMetrics ? 'bg-green-500' : 'bg-gray-400'}`}></span>
              Top Partnerships
            </span>
            <span class={`flex items-center ${chartsInitialized.realtimePerformance ? 'text-green-600' : 'text-gray-500'}`}>
              <span class={`w-2 h-2 rounded-full mr-1 ${chartsInitialized.realtimePerformance ? 'bg-green-500' : 'bg-gray-400'}`}></span>
              Real-time Performance
            </span>
          </div>
        </div>
      </div>

      {/* Dashboard Controls */}
      <div class="mb-6 space-y-4">
        {/* Primary Controls */}
        <div class="flex flex-wrap items-center justify-between gap-4">
          <div class="flex items-center space-x-4">
            <label class="flex flex-col">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Time Range</span>
              <select
                value={selectedTimeFrame}
                onChange={(e) => {
                  setSelectedTimeFrame(e.currentTarget.value);
                  onTimeFrameChange?.(e.currentTarget.value);
                }}
                class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                aria-label="Select time range for analytics data"
              >
                <option value="7d">Last 7 Days</option>
                <option value="30d">Last 30 Days</option>
                <option value="90d">Last 90 Days</option>
              </select>
            </label>

            <label class="flex flex-col">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Metric</span>
              <select
                value={selectedMetric}
                onChange={(e) => {
                  setSelectedMetric(e.currentTarget.value);
                  onMetricChange?.(e.currentTarget.value);
                }}
                class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                aria-label="Select metric to display in charts"
              >
                <option value="revenue">Revenue</option>
                <option value="events">Events</option>
                <option value="conversions">Conversions</option>
              </select>
            </label>
          </div>

          <div class="flex items-center space-x-3">
            {/* Real-time Status Indicator */}
            <div class="flex items-center space-x-2">
              <div class={`w-3 h-3 rounded-full ${
                connectionStatus === 'connected' ? 'bg-green-500' :
                connectionStatus === 'connecting' ? 'bg-yellow-500' : 'bg-red-500'
              }`} aria-hidden="true"></div>
              <span class="text-sm text-gray-600 dark:text-gray-300">
                {connectionStatus === 'connected' ? 'Live' :
                 connectionStatus === 'connecting' ? 'Connecting...' : 'Offline'}
              </span>
              {lastUpdate && (
                <span class="text-xs text-gray-500 dark:text-gray-400">
                  Last update: {lastUpdate}
                </span>
              )}
            </div>

            <label class="flex items-center">
              <input
                type="checkbox"
                checked={isRealTimeEnabled}
                onChange={(e) => setIsRealTimeEnabled(e.currentTarget.checked)}
                class="mr-2 focus:ring-2 focus:ring-blue-500"
                aria-describedby="realtime-help"
              />
              <span class="text-sm text-gray-700 dark:text-gray-300">Real-time Updates</span>
              <span id="realtime-help" class="sr-only">
                Enable or disable real-time data streaming for live analytics updates
              </span>
            </label>

            <button
              type="button"
              onClick={() => onDataExport?.(analyticsData, 'csv')}
              class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm font-medium transition-colors focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              aria-label="Export analytics data as CSV file"
            >
              Export Data
            </button>
          </div>
        </div>

        {/* Enhanced Cohort Filtering Controls */}
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between mb-3">
            <h3 class="text-sm font-semibold text-gray-900 dark:text-white">Cohort Analysis Filters</h3>
            <label class="flex items-center">
              <input
                type="checkbox"
                checked={showComparison}
                onChange={(e) => setShowComparison(e.currentTarget.checked)}
                class="mr-2 focus:ring-2 focus:ring-blue-500"
              />
              <span class="text-sm text-gray-700 dark:text-gray-300">Show Comparison View</span>
            </label>
          </div>

          <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
            <label class="flex flex-col">
              <span class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Cohort Type</span>
              <select
                value={cohortFilters.cohortType}
                onChange={(e) => setCohortFilters(prev => ({ ...prev, cohortType: e.currentTarget.value as CohortFilters['cohortType'] }))}
                class="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-1 focus:ring-blue-500"
              >
                <option value="all">All Types</option>
                <option value="organic">Organic</option>
                <option value="partnership_acquisition">Partnership Acquisition</option>
                <option value="cross_tenant_collaboration">Cross-Tenant Collaboration</option>
                <option value="referral_program">Referral Program</option>
                <option value="joint_marketing_campaign">Joint Marketing</option>
              </select>
            </label>

            <label class="flex flex-col">
              <span class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Partnership Type</span>
              <select
                value={cohortFilters.partnershipType}
                onChange={(e) => setCohortFilters(prev => ({ ...prev, partnershipType: e.currentTarget.value as CohortFilters['partnershipType'] }))}
                class="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-1 focus:ring-blue-500"
              >
                <option value="all">All Partnerships</option>
                <option value="strategic">Strategic</option>
                <option value="affiliate">Affiliate</option>
                <option value="integration">Integration</option>
              </select>
            </label>

            <label class="flex flex-col">
              <span class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Acquisition Channel</span>
              <select
                value={cohortFilters.acquisitionChannel}
                onChange={(e) => setCohortFilters(prev => ({ ...prev, acquisitionChannel: e.currentTarget.value as CohortFilters['acquisitionChannel'] }))}
                class="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-1 focus:ring-blue-500"
              >
                <option value="all">All Channels</option>
                <option value="direct">Direct</option>
                <option value="referral">Referral</option>
                <option value="collaboration">Collaboration</option>
                <option value="cross_promotion">Cross Promotion</option>
              </select>
            </label>

            <label class="flex flex-col">
              <span class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Analysis Period</span>
              <select
                value={cohortFilters.timeRange}
                onChange={(e) => setCohortFilters(prev => ({ ...prev, timeRange: e.currentTarget.value as CohortFilters['timeRange'] }))}
                class="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-1 focus:ring-blue-500"
              >
                <option value="30d">30 Days</option>
                <option value="90d">90 Days</option>
                <option value="180d">180 Days</option>
                <option value="1y">1 Year</option>
              </select>
            </label>

            <label class="flex flex-col">
              <span class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Comparison Mode</span>
              <select
                value={cohortFilters.comparisonMode}
                onChange={(e) => setCohortFilters(prev => ({ ...prev, comparisonMode: e.currentTarget.value as CohortFilters['comparisonMode'] }))}
                class="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-1 focus:ring-blue-500"
              >
                <option value="absolute">Absolute Values</option>
                <option value="vs_organic">vs Organic</option>
                <option value="vs_average">vs Average</option>
                <option value="vs_top_quartile">vs Top Quartile</option>
              </select>
            </label>
          </div>
        </div>
      </div>

      {/* Charts Grid */}
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6" role="region" aria-label="Analytics Charts">
        {/* Network Trends Chart */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Network Trends - {selectedMetric === 'revenue' ? 'Revenue' : 'Events'}
          </h3>
          <div
            ref={networkTrendsRef}
            class="w-full"
            role="img"
            aria-label={`Network trends chart showing ${selectedMetric} over ${selectedTimeFrame}`}
            tabIndex={0}
          ></div>
        </div>

        {/* Partnership Metrics Chart */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Top Partnerships by Revenue
          </h3>
          <div
            ref={partnershipMetricsRef}
            class="w-full"
            role="img"
            aria-label="Bar chart showing top partnerships ranked by revenue performance"
            tabIndex={0}
          ></div>
        </div>

        {/* Real-time Performance Chart */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Real-time Performance
            {isRealTimeEnabled && connectionStatus === 'connected' && (
              <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                Live
              </span>
            )}
          </h3>
          <div
            ref={realtimePerformanceRef}
            class="w-full"
            role="img"
            aria-label="Real-time performance area chart showing live marketplace activity in 15-minute intervals"
            tabIndex={0}
          ></div>
        </div>

        {/* Cohort Analysis Chart */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Cohort Analysis
            {analyticsData.advanced_analytics?.cohort_analysis && (
              <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                Advanced
              </span>
            )}
          </h3>
          <div
            ref={cohortAnalysisRef}
            class="w-full"
            role="img"
            aria-label="Cohort analysis visualization showing customer retention patterns over time"
            tabIndex={0}
          ></div>
        </div>
      </div>

      {/* Enhanced Analytics Status */}
      {analyticsData.advanced_analytics && (
        <div class="mt-6 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Marketplace Analytics Status</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div class="text-center">
              <div class="font-medium text-blue-600 dark:text-blue-400">
                {analyticsData.advanced_analytics?.cohort_analysis ? '✓' : '✗'}
              </div>
              <div class="text-gray-600 dark:text-gray-300">Cohort Analysis</div>
            </div>
            <div class="text-center">
              <div class="font-medium text-green-600 dark:text-green-400">
                {analyticsData.advanced_analytics?.clv_analysis ? '✓' : '✗'}
              </div>
              <div class="text-gray-600 dark:text-gray-300">CLV Analysis</div>
            </div>
            <div class="text-center">
              <div class="font-medium text-purple-600 dark:text-purple-400">
                {analyticsData.advanced_analytics?.funnel_analysis ? '✓' : '✗'}
              </div>
              <div class="text-gray-600 dark:text-gray-300">Funnel Analysis</div>
            </div>
            <div class="text-center">
              <div class="font-medium text-orange-600 dark:text-orange-400">
                {analyticsData.advanced_analytics?.predictive_analysis ? '✓' : '✗'}
              </div>
              <div class="text-gray-600 dark:text-gray-300">Predictive Analytics</div>
            </div>
          </div>

          {/* Partnership Performance Summary */}
          {analyticsData.advanced_analytics?.cohort_analysis?.partnership_cohorts && (
            <div class="mt-4 pt-4 border-t border-blue-200 dark:border-blue-700">
              <div class="flex items-center justify-between text-sm">
                <span class="text-gray-600 dark:text-gray-300">
                  Partnership Performance vs Organic:
                  <span class="font-semibold text-green-600 dark:text-green-400 ml-1">
                    +{(((analyticsData.advanced_analytics?.cohort_analysis?.overview.partnershipRetentionAverage || 0.72) / (analyticsData.advanced_analytics?.cohort_analysis?.overview.organicRetentionBaseline || 0.58) - 1) * 100).toFixed(1)}%
                  </span>
                </span>
                <span class="text-gray-600 dark:text-gray-300">
                  Active Partnerships:
                  <span class="font-semibold text-blue-600 dark:text-blue-400 ml-1">
                    {analyticsData.advanced_analytics?.cohort_analysis?.partnership_cohorts?.length || 0}
                  </span>
                </span>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
